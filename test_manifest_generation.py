#!/usr/bin/env python3
"""
Test script to verify the manifest generation with Supabase keys
"""

import json
import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from classes.NodeDB import NodeDB

def test_manifest_generation():
    """Test the create_initial_manifest method with Supabase keys"""
    
    # Create a NodeDB instance (we don't need actual DB connection for this test)
    node_db = NodeDB()
    
    # Sample data structure that should be created by the start_project endpoint
    sample_data = {
        "overview": {
            "project_name": "Colour picker",
            "description": "Create a colour picker application",
            "frontend_framework": "react",
            "backend_framework": "fastapi"
        },
        "containers": [
            {
                "container_type": "backend",
                "container_name": "colour-picker-backend",
                "description": "FastAPI backend for managing color history, user preferences, and potential future features such as saving favorite palettes.",
                "interfaces": "RESTful API endpoints for retrieving, saving, and managing color data; communicates with frontend.",
                "framework": "fastapi",
                "dependent_containers": [],
                "env": {
                    "SERVICE_ROLE_KEY": "grkgn3300022gAAAAABoTDfkj1vidar5rKBvwGT7Xv74fvPweCo_Kk2qlhlWwWMzsGx9zRLfI3vD5arsSS",
                    "ANON_KEY": "gAAAAABoTDfkj1vidar5rKBvwGT7Xv74fvPweCo_Kk2qlhlWwWMzsGx9zRLfI3vD5arsSS"
                }
            },
            {
                "container_type": "frontend",
                "container_name": "colour-picker-frontend",
                "description": "React-based frontend for the Colour Picker application, allowing users to select colors and view their codes.",
                "interfaces": "User-facing web interface for color selection and display; interacts with backend for potential color management or saving functionality.",
                "framework": "react",
                "dependent_containers": ["colour-picker-backend"],
                "env": {
                    "anon-key": "gAAAAABoTDfkj1vidar5rKBvwGT7Xv74fvPweCo_Kk2qlhlWwWMzsGx9zRLfI3vD5arsSS",
                    "service_role_key": "grkgn3300022gAAAAABoTDfkj1vidar5rKBvwGT7Xv74fvPweCo_Kk2qlhlWwWMzsGx9zRLfI3vD5arsSS"
                }
            }
        ]
    }
    
    # Generate the manifest
    try:
        manifest = node_db.create_initial_manifest(sample_data)
        
        print("Generated Manifest:")
        print("=" * 50)
        print(json.dumps(manifest, indent=2))
        print("=" * 50)
        
        # Verify the structure
        assert "appName" in manifest
        assert "description" in manifest
        assert "version" in manifest
        assert "containers" in manifest
        assert isinstance(manifest["containers"], list)
        
        # Check that we have both backend and frontend containers
        container_types = [c.get("container_type") for c in manifest["containers"]]
        assert "backend" in container_types
        assert "frontend" in container_types
        
        # Check backend container has the correct env variables
        backend_container = next(c for c in manifest["containers"] if c.get("container_type") == "backend")
        assert "env" in backend_container
        assert "SERVICE_ROLE_KEY" in backend_container["env"]
        assert "ANON_KEY" in backend_container["env"]
        
        # Check frontend container has the correct env variables
        frontend_container = next(c for c in manifest["containers"] if c.get("container_type") == "frontend")
        assert "env" in frontend_container
        assert "anon-key" in frontend_container["env"]
        assert "service_role_key" in frontend_container["env"]
        
        print("✅ All tests passed!")
        print(f"✅ Backend env keys: {list(backend_container['env'].keys())}")
        print(f"✅ Frontend env keys: {list(frontend_container['env'].keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_manifest_generation()
    sys.exit(0 if success else 1)
