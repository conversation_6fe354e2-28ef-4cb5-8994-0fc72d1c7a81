from pydantic_settings import BaseSettings ,PydanticBaseSettingsSource
from pydantic import Field
from pydantic.fields import FieldInfo
from typing import List, Tuple, Type, Any, Dict
import asyncio
import os
import boto3

STAGE = os.environ.get("STAGE",None)
class AwsSsmSettings(PydanticBaseSettingsSource):
    def __init__(self, settings_cls: Type[BaseSettings]):
        super().__init__(settings_cls)
        self.ssm = boto3.client('ssm')
        self.stage = os.environ.get("BATCH_JOB_STAGE", "dev").lower()
        print(self.stage)
        print("Getting info from ssm")
    def get_field_value(self, field: FieldInfo, field_name: str) -> Tuple[Any, str, bool]:
        try:
            parameter = self.ssm.get_parameter(Name=f"/kavia-backend/{self.stage}/{field_name}", WithDecryption=True)
            value = parameter['Parameter']['Value']
            return value, field_name, self.field_is_complex(field)
        except self.ssm.exceptions.ParameterNotFound:
            # Parameter doesn't exist in SSM, return None
            return None, field_name, False

    def __call__(self) -> Dict[str, Any]:
        config = {}
        for field_name, field in self.settings_cls.model_fields.items():
            value, _, is_complex = self.get_field_value(field, field_name)
            if value is not None:
                config[field_name] = self.prepare_field_value(field_name, field, value, is_complex)
        return config
    
class Settings(BaseSettings):
    
    # Application version
    APP_VERSION: str = Field(env="APP_VERSION", default="1.0.0", description="Application version number")
    REDIS_HOST: str = Field(env="REDIS_HOST", description="Redis IP", default="")
    REDIS_PASSWORD: str = Field(env="REDIS_PASSWORD", description="Redis password", default="")
        
    # MongoDB connection variables adjusted
    MONGO_CONNECTION_URI: str = Field(env="MONGO_CONNECTION_URI",  description="The url of the MongoDB")
    MONGO_DB_NAME: str = Field(env="MONGO_DB_NAME", default="", description="Default database name")
    DOMAIN: str = Field(env="DOMAIN", default="http://localhost:3000", description="Domain which is connected to front-end")
    MONGO_TENANT_DB_NAME: str = Field(env="MONGO_TENANT_DB_NAME", default="kaviaroot", description="Tenant database name")
    
    # AWS Access
    AWS_REGION: str = Field(env="REGION", default="us-east-1", description="Region of the aws")
    AWS_DEPLOYMENT_REGION: str = Field(env="AWS_DEPLOYMENT_REGION", default="us-east-1", description="Region of the aws deployment")
    AWS_ACCESS_KEY_ID: str = Field(env="AWS_ACCESS_KEY_ID", description="Access key of aws account")
    AWS_SECRET_ACCESS_KEY: str = Field(env="AWS_SECRET_ACCESS_KEY", description="Secret key of aws account")
    AWS_AMI: str = Field(env="AWS_AMI",description="AMI for new instance creation!")
    
    # COGNITO
    #AllOWED_TENET
    ALLOWED_TENANTS: str = Field(env="ALLOWED_TENANTS",description="Allowed tenants for the pods and logs use", default="")
    
    # AWS_COGNITO_USER_POOL_ID: str = Field(env="AWS_COGNITO_USER_POOL_ID", description="User pool id of cognito")
    AWS_COGNITO_USER_POOL_ID: str =  Field(env="AWS_COGNITO_USER_POOL_ID", description="User pool id of cognito")
    AWS_COGNITO_APP_CLIENT_ID: str = Field(env="AWS_COGNITO_APP_CLIENT_ID",description="App client id of cognito")
    
    # Google OAuth configuration
    GOOGLE_CLIENT_ID: str = Field(env="GOOGLE_CLIENT_ID", default="", description="Google OAuth client ID")
    GOOGLE_CLIENT_SECRET: str = Field(env="GOOGLE_CLIENT_SECRET", default="", description="Google OAuth client secret")
    GOOGLE_REDIRECT_URI: str = Field(env="GOOGLE_REDIRECT_URI", description="Google OAuth redirect URI", default="http://localhost:3000/users/sso/callback/google")
    
    # Google SSO Configuration
    ENABLE_PASSWORDLESS_GOOGLE_AUTH: bool = Field(env="ENABLE_PASSWORDLESS_GOOGLE_AUTH", default=True, description="Enable password-less authentication for Google SSO users")
    
    # Neo4j Database Credentials
    NEO4J_CONNECTION_URI: str = Field(env="NEO4J_CONNECTION_URI", description="Neo4j connection URI")
    NEO4J_USER: str = Field(env="NEO4J_USER", description="User for Neo4j database")
    NEO4J_PASSWORD: str = Field(env="NEO4J_PASSWORD", description="Password for Neo4j database")

    # API Keys adjusted to new provided variables
    CELERY_BROKER_URL: str = Field(env="CELERY_BROKER_URL", description="Broker URL for Celery")
    OPENAI_API_KEY: str = Field(env="OPENAI_API_KEY", description="API key for OpenAI services")
    STAGE: str = Field(env="STAGE", default="develop", description="Stage of the application")    
    AWS_ACCOUNT_ID: str = Field(env="AWS_ACCOUNT_ID")
    
    DATADOG_API_KEY:str =Field(env="DATADOG_API_KEY")
    DATADOG_APP_KEY:str =Field(env="DATADOG_APP_KEY")
    DATADOG_SERVER:str = Field(env="DATADOG_SERVER", default="us5.datadoghq.com")
    GITHUB_ACCESS_TOKEN:str = Field(env="GITHUB_ACCESS_TOKEN")
    WEBSOCKET_URI:str = Field(env="WEBSOCKET_URI", default="wss://kavia-websocket.cloud.kavia.ai")
    KAVIA_ROOT_TENANT_ID:str = Field(env="KAVIA_ROOT_TENANT_ID", default="T0000")
    KAVIA_SUPER_TENANT_ID:str = Field(env="KAVIA_SUPER_TENANT_ID", default="")
    KAVIA_B2C_CLIENT_ID:str = Field(env="KAVIA_B2C_CLIENT_ID", default="b2c")
    TENANT_SALT:str = Field(env="TENANT_SALT", default="")
    CODEGEN:bool = Field(env="CODEGEN", default=False)
    VSCODE_CLOUD_URL:str = Field(env="VSCODE_CLOUD_URL")

    GITHUB_CLIENT_ID:str = Field(env="GITHUB_CLIENT_ID", default="test")
    GITHUB_CLIENT_SECRET:str = Field(env="GITHUB_CLIENT_SECRET", default="test")
    GITHUB_REDIRECTION_URI:str = Field(env="GITHUB_REDIRECTION_URI", default="http://localhost:3000/scm/connection")


    GITHUB_CLIENT_ID_CODEGEN:str = Field(env="GITHUB_CLIENT_ID_CODEGEN", default="test")
    GITHUB_CLIENT_SECRET_CODEGEN:str = Field(env="GITHUB_CLIENT_SECRET_CODEGEN", default="test")
    GITHUB_REDIRECTION_URI_CODEGEN:str = Field(env="GITHUB_REDIRECTION_URI_CODEGEN", default="http://localhost:3000/scm/connection")

    GITLAB_CLIENT_ID_CODEGEN:str = Field(env="GITLAB_CLIENT_ID_CODEGEN", default="test")
    GITLAB_CLIENT_SECRET_CODEGEN:str = Field(env="GITLAB_CLIENT_SECRET_CODEGEN", default="test")
    GITLAB_REDIRECTION_URI_CODEGEN:str = Field(env="GITLAB_REDIRECTION_URI_CODEGEN", default="http://localhost:3000/scm/connection")

    ANTHROPIC_API_KEY:str = Field(env="ANTHROPIC_API_KEY",default="")
    S3_BUILD_ARTIFACTS_BUCKET: str = Field(env="S3_BUILD_ARTIFACTS_BUCKET", default="kavia-deployment-artifacts")
    
    STRIPE_SECRET_KEY:str = Field(env="STRIPE_SECRET_KEY",default="")
    STRIPE_PUBLISHABLE_KEY:str = Field(env="STRIPE_PUBLISHABLE_KEY",default="")
    
    VERTEX_PROJECT_ID:str = Field(env="VERTEX_PROJECT_ID",default="vertex-ai-sandbox-447902")
    VERTEX_LOCATION_ID:str = Field(env="VERTEX_LOCATION_ID",default="us-east5")

    #supabase 
    SUPABASE_OAUTH_CLIENT_ID:str = Field(env="SUPABASE_OAUTH_CLIENT_ID")
    SUPABASE_OAUTH_CLIENT_SECRET:str = Field(env="SUPABASE_OAUTH_CLIENT_SECRET")
    SUPABASE_OAUTH_REDIRECT_URI:str = Field(env="SUPABASE_OAUTH_REDIRECT_URI")
    SUPABASE_OAUTH_AUTHORIZE_URL:str = Field(env="SUPABASE_OAUTH_AUTHORIZE_URL")
    SUPABASE_OAUTH_TOKEN_URL:str = Field(env="SUPABASE_OAUTH_TOKEN_URL")
    ENCRYPTION_KEY:str = Field(env="ENCRYPTION_KEY")


    # Email/SES Configuration
    SES_SMTP_USERNAME: str = Field(env="SES_SMTP_USERNAME", default="", description="SMTP username for AWS SES")
    SES_SMTP_PASSWORD: str = Field(env="SES_SMTP_PASSWORD", default="", description="SMTP password for AWS SES")
    SES_EMAIL_DOMAIN: str = Field(env="SES_EMAIL_DOMAIN", default="kavia.ai", description="Default email domain for sending emails")
    SES_SOURCE_ARN: str = Field(env="SES_SOURCE_ARN", default="arn:aws:ses:us-east-1:058264095463:identity/kavia.ai", description="Source ARN for AWS SES")
    SES_FROM_EMAIL: str = Field(env="SES_FROM_EMAIL", default="<EMAIL>", description="From email for AWS SES")
    SES_REPLY_TO: str = Field(env="SES_REPLY_TO", default="<EMAIL>", description="Reply to email for AWS SES")
    KAVIA_ROOT_DB_NAME:str = f"{STAGE}_kaviaroot"
    
    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: Type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> Tuple[PydanticBaseSettingsSource, ...]:
        
        
        if os.environ.get("STAGE", "").lower() == "production":
            return env_settings, init_settings, file_secret_settings
        if os.environ.get("BATCH_JOB_TRIGGER"):
            return dotenv_settings, env_settings, init_settings, file_secret_settings
           
        return dotenv_settings, env_settings, init_settings, file_secret_settings
 
    class Config:
        validate_assignment = True
        case_sensitive = True
        env_file = f".env.{STAGE}" if STAGE else ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # Ignore extra variables

settings = Settings()
print("Is codegen : ", settings.CODEGEN)